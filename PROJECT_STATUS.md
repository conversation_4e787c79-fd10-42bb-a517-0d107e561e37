# TeleStore Project Status & Documentation

## 📊 Tổng quan dự án

TeleStore là hệ thống quản lý file cá nhân sử dụng Telegram làm kho lưu trữ đám mây miễn phí với giao diện web trực quan tương tự Google Drive/Dropbox.

## ✅ Tính năng đã hoàn thành

### 🔐 Hệ thống xác thực (Authentication)
- **Traditional Login**: Đă<PERSON> nhập bằng username/password ✅
- **Google OAuth**: Đăng nhập bằng tài khoản Google ✅
- **Telegram Login**: Đăng nhập bằng tài khoản Telegram ✅
- **JWT Token Management**: Quản lý token với Redis ✅
- **User Profile Management**: Quản lý thông tin người dùng ✅
- **Email Verification**: Xác thực email cho tài khoản local ✅
- **OAuth Toggle**: Bật/tắt OAuth features trong config ✅
- **Password Validation**: <PERSON><PERSON><PERSON> tra mật khẩu tối thiểu 6 ký tự ✅
- **Password Strength**: Hi<PERSON>n thị độ mạnh mật khẩu (yếu/trung bình/mạnh) ✅
- **Dark Mode Login**: Trang đăng nhập/đăng ký hỗ trợ dark/light mode ✅

### 📁 Quản lý File & Folder
- **File Upload**: Upload đa file với progress bar ✅
- **Folder Management**: Tạo, đổi tên, xóa thư mục ✅
- **File Download**: Tải file từ Telegram với user authentication ✅
- **File/Folder Rename**: Đổi tên file và thư mục ✅
- **File/Folder Delete**: Xóa file và thư mục với duplicate key handling ✅
- **Breadcrumb Navigation**: Điều hướng qua các thư mục ✅
- **Search Functionality**: Tìm kiếm file và thư mục ✅
- **Context Menu**: Menu chuột phải cho file/folder ✅
- **Home Navigation**: Click vào logo/brand để về trang chủ ✅

### 🎨 Giao diện người dùng (UI/UX)
- **Responsive Design**: Giao diện responsive với Material-UI ✅
- **Dark Mode**: Chế độ tối hoàn chỉnh với localStorage persistence ✅
- **Theme Persistence**: Dark/Light mode được lưu vĩnh viễn ✅
- **Dropbox-inspired Interface**: Thiết kế lấy cảm hứng từ Dropbox ✅
- **File Preview Thumbnails**: Hiển thị thumbnail cho ảnh ✅
- **Upload Queue Preview**: Xem trước file trong queue upload ✅
- **Loading States**: Trạng thái loading cho các thao tác ✅
- **Beautiful UI**: Giao diện đẹp, thân thiện người dùng ✅
- **Storage Indicator**: Hiển thị dung lượng sử dụng ở góc dưới trái ✅
- **Multi-language Support**: Hỗ trợ đa ngôn ngữ (EN, VI, ZH) ✅
- **Settings Page**: Trang cài đặt với language selector và theme toggle ✅

### 🔧 Hệ thống Backend
- **Node.js/Express**: API server với Express framework ✅
- **MongoDB**: Database với user-specific data ✅
- **Redis**: Caching và session management ✅
- **Telegram Bot API**: Tích hợp với Telegram để lưu trữ ✅
- **File Type Support**: Hỗ trợ tất cả loại file (không chỉ ảnh/video) ✅
- **Security**: CSRF protection, input validation, XSS protection ✅
- **Error Handling**: Xử lý lỗi toàn diện ✅
- **Storage Management**: Quản lý dung lượng với BigInt support (1000TB+) ✅
- **Rich Metadata**: Lưu đầy đủ metadata từ Telegram API ✅

### 📱 File Preview System
- **Image Preview**: Xem trước ảnh trực tiếp ✅
- **Video Preview**: Xem trước video với controls ✅
- **Audio Preview**: Xem trước audio với controls ✅
- **Text File Preview**: Xem trước file text với syntax highlighting ✅
- **Single-click Preview**: Preview bằng 1 click thay vì double-click ✅

### 💾 Storage Management System
- **BigInt Storage Support**: Hỗ trợ storage lên đến 1000TB+ an toàn ✅
- **Real-time Usage Tracking**: Theo dõi dung lượng sử dụng real-time ✅
- **Storage Quota Management**: Kiểm tra quota trước upload, từ chối nếu vượt quá ✅
- **Storage Analytics**: Thống kê chi tiết files, folders, usage percentage ✅
- **Auto Storage Updates**: Tự động cập nhật khi upload/delete files/folders ✅
- **Storage Sync**: Manual sync storage stats cho maintenance ✅
- **Visual Storage Indicator**: Hiển thị progress bar với cảnh báo khi gần hết ✅

## ⚠️ Lỗi hiện tại cần sửa

### 🔴 PDF Preview Issues
- **PDF không hiển thị**: PDF files không load được trong iframe
- **Loading state**: PDF loading indicator không hoạt động đúng
- **Error handling**: Xử lý lỗi PDF chưa tối ưu
- **Browser compatibility**: Một số browser không hỗ trợ PDF preview

### 🔴 Office File Preview Issues
- **Google Docs Viewer**: Google Docs viewer không load được file từ Telegram URL
- **CORS Issues**: Vấn đề CORS khi embed Office files
- **Authentication**: Google viewer không thể access file cần authentication
- **Fallback mechanism**: Chưa có fallback tốt khi preview thất bại

### 🔴 Authentication & Security
- **Token expiration**: Chưa handle token expiration gracefully
- **Session management**: Session cleanup chưa tối ưu
- **OAuth error handling**: Error handling cho OAuth flow chưa hoàn thiện

### 🔴 File Management
- **Large file handling**: Upload file lớn (>20MB) có thể timeout
- **Progress tracking**: Progress bar không accurate cho file lớn
- **Concurrent uploads**: Upload nhiều file cùng lúc có thể gây lỗi

## 🚧 Tính năng chưa hoàn thành

### 📋 Cần làm tiếp
- [ ] **Fix PDF Preview**: Sửa lỗi PDF không hiển thị trong browser
- [ ] **Fix Office Preview**: Sửa lỗi Office file preview với Google Docs Viewer
- [ ] **Improve Error Handling**: Cải thiện xử lý lỗi cho preview system
- [ ] **File Sharing**: Chia sẻ file/folder với người khác
- [ ] **File Versioning**: Quản lý phiên bản file
- [ ] **Bulk Operations**: Thao tác hàng loạt (select multiple, bulk delete)
- [ ] **Advanced Search**: Tìm kiếm nâng cao (theo loại file, kích thước, ngày)
- [ ] **File Tags**: Gắn tag cho file để tổ chức tốt hơn
- [ ] **Trash/Recycle Bin**: Thùng rác để khôi phục file đã xóa
- [ ] **File Compression**: Nén file trước khi upload
- [ ] **Duplicate Detection**: Phát hiện file trùng lặp

### 🔮 Tính năng tương lai
- [ ] **Mobile App**: Ứng dụng mobile React Native
- [ ] **Desktop App**: Ứng dụng desktop với Electron
- [ ] **Auto Sync**: Đồng bộ tự động từ điện thoại/máy tính
- [ ] **AI Integration**: Tích hợp AI để auto-tag và mô tả file
- [ ] **Local Bot API**: Hỗ trợ file >50MB với Local Bot API Server
- [ ] **Multi-user Management**: Quản lý đa người dùng với roles
- [ ] **Backup & Restore**: Sao lưu và khôi phục dữ liệu
- [ ] **API Documentation**: Tài liệu API đầy đủ với Swagger

## 🏗️ Kiến trúc hệ thống

### Backend Structure
```
backend/
├── lib/
│   ├── models/          # MongoDB models (User, File, Folder)
│   ├── routes/          # API routes (auth, files, folders, browse, user/storage)
│   ├── services/        # Business logic (telegram, cache, email, storageService)
│   ├── middleware/      # Express middleware (auth, upload, error)
│   └── connections/     # Database connections (MongoDB, Redis)
├── config/              # Configuration files
└── index.js            # Main server file
```

### Frontend Structure
```
frontend/src/
├── components/          # React components
│   ├── FileGrid.tsx    # Main file grid with preview
│   ├── Header.tsx      # Navigation header
│   ├── LoginPage.tsx   # Authentication page
│   └── ...
├── services/           # API services
├── contexts/           # React contexts (Auth, Theme)
├── types/              # TypeScript types
└── utils/              # Utility functions
```

## 🔧 Cấu hình hiện tại

### Environment Variables
```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=configured ✅
TELEGRAM_CHAT_ID=configured ✅

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/tele-store ✅
REDIS_HOST=localhost ✅
REDIS_PORT=6379 ✅

# OAuth Configuration
GOOGLE_CLIENT_ID=configured ✅
GOOGLE_CLIENT_SECRET=configured ✅
TELEGRAM_BOT_USERNAME=configured ✅

# Application Configuration
NODE_ENV=development ✅
PORT=3000 ✅
SECRET_KEY=configured ✅
```

### File Upload Limits
- **Max file size**: 50MB (Telegram Bot API limit)
- **Supported file types**: All file types (không giới hạn extension)
- **Concurrent uploads**: 10 files maximum
- **Blocked extensions**: .exe, .bat, .cmd, .scr (security reasons)

## 📊 Database Schema

### User Model
```javascript
{
  _id: ObjectId,
  username: String,
  email: String,
  password: String (hashed),
  provider: String, // 'local', 'google', 'telegram'
  providerId: String,
  isEmailVerified: Boolean,
  role: String,
  status: Number,
  // Storage tracking (using String for BigInt support)
  storageUsed: String, // bytes as string (default: '0')
  storageQuota: String, // bytes as string (default: '1099511627776000' = 1000TB)
  fileCount: Number, // total files count
  folderCount: Number, // total folders count
  createdAt: Date,
  updatedAt: Date
}
```

### File Model
```javascript
{
  _id: ObjectId,
  telegramFileId: String,
  originalFileName: String,
  fileSize: Number,
  mimeType: String,
  parentId: ObjectId, // folder ID
  ownerId: ObjectId, // user ID
  uploadDate: Date,
  isDeleted: Boolean,
  // Rich Telegram metadata
  telegramMetadata: Object, // Full Telegram API response
  fileMetadata: {
    width: Number, // for images/videos
    height: Number, // for images/videos
    duration: Number, // for videos/audio
    thumbnail: String, // thumbnail file_id if available
    fileUniqueId: String, // Telegram's unique file identifier
    fileName: String, // filename from Telegram
    performer: String, // for audio files
    title: String, // for audio files
    type: Object // additional type-specific metadata
  }
}
```

### Folder Model
```javascript
{
  _id: ObjectId,
  folderName: String,
  parentId: ObjectId,
  ownerId: ObjectId, // user ID
  createdAt: Date,
  isDeleted: Boolean
}
```

## 🚀 Deployment

### Development
```bash
# Backend
cd backend && npm start

# Frontend
cd frontend && npm start
```

### Production (Docker)
```bash
docker-compose up -d
```

## 📝 Notes cho Developer

### Khi thêm tính năng mới:
1. **Backend**: Thêm route trong `backend/lib/routes/api/`
2. **Frontend**: Thêm component trong `frontend/src/components/`
3. **API Service**: Cập nhật `frontend/src/services/api.ts`
4. **Types**: Cập nhật TypeScript types trong `frontend/src/types/`
5. **Update this file**: Cập nhật PROJECT_STATUS.md

### Khi fix bug:
1. **Identify**: Xác định root cause của bug
2. **Test**: Viết test case để reproduce bug
3. **Fix**: Implement fix
4. **Verify**: Verify fix hoạt động
5. **Update**: Cập nhật status trong file này

### Priority cho việc fix:
1. **High**: PDF và Office file preview (ảnh hưởng UX chính)
2. **Medium**: Authentication improvements
3. **Low**: Performance optimizations

## 🆕 Recent Updates (2025-01-18)

### ✅ Completed Features
1. **Dark Mode Persistence**: Theme preference được lưu trong localStorage
2. **BigInt Storage Support**: Hỗ trợ storage lên đến 1000TB+ với String + BigInt
3. **Rich Telegram Metadata**: Lưu đầy đủ thông tin từ Telegram API response
4. **Storage Management System**:
   - Real-time storage tracking
   - Quota management với cảnh báo
   - Visual storage indicator ở góc dưới trái
   - Auto-update khi upload/delete
   - Manual sync storage stats
5. **Enhanced File Model**: Thêm telegramMetadata và fileMetadata fields
6. **Storage Service**: Service chuyên dụng cho storage operations với BigInt support

### 🔧 Technical Improvements
- **String-based Storage**: Sử dụng String thay vì Number để tránh giới hạn JavaScript
- **MongoDB Aggregation**: Tính toán storage hiệu quả
- **Bulk Operations**: Xử lý delete folder với nhiều files
- **API Endpoints**: `/user/storage`, `/user/storage-sync`
- **Error Handling**: Graceful handling cho storage operations

---

## 🆕 Cập nhật mới nhất (2025-01-18)

### ✅ Các vấn đề đã được giải quyết:

1. **🔧 Sửa lỗi Download**
   - Cập nhật download route để kiểm tra user authentication
   - Chỉ cho phép user download file của chính họ
   - Cải thiện security cho file access

2. **🎨 Dark/Light Mode cho Login Page**
   - Chuyển LoginPage sang sử dụng Material-UI theme system
   - Tự động detect system preference (dark/light)
   - Đồng bộ với theme setting từ localStorage
   - Giao diện login/register responsive và đẹp mắt

3. **🔐 Password Validation & Strength**
   - Kiểm tra mật khẩu tối thiểu 6 ký tự (frontend + backend)
   - Hiển thị độ mạnh mật khẩu real-time (Very Weak → Strong)
   - Progress bar màu sắc thể hiện độ mạnh
   - Feedback suggestions để cải thiện mật khẩu
   - Kiểm tra common patterns và repeated characters

4. **🌍 Hệ thống đa ngôn ngữ (i18n)**
   - Tích hợp react-i18next với language detection
   - Hỗ trợ 3 ngôn ngữ: English, Tiếng Việt, 中文
   - Auto-detect browser language với fallback
   - Lưu language preference vào localStorage

5. **⚙️ Settings Page**
   - Trang cài đặt hoàn chỉnh với Material-UI
   - Language selector với flag icons
   - Theme toggle (Light/Dark) với switch
   - Placeholder cho các settings khác (Profile, Security, Storage, etc.)
   - Success notifications khi thay đổi settings

6. **🏠 Home Navigation**
   - Click vào logo/brand "TeleStore" để về trang chủ
   - Settings icon trong header link đến trang settings
   - Improved navigation UX

### 🔧 Technical Changes:

- **Frontend Dependencies**: Thêm react-i18next, i18next, i18next-browser-languagedetector
- **New Components**:
  - `SettingsPage.tsx` - Trang cài đặt
  - `LoginPageNew.tsx` - Login page với Material-UI
  - `passwordValidation.ts` - Utility cho password validation
- **i18n Structure**:
  - `/src/i18n/` - i18n configuration
  - `/src/i18n/locales/` - Translation files (en.json, vi.json, zh.json)
- **Backend Validation**: Thêm password length validation trong register API
- **Route Updates**: Thêm `/settings` route trong App.tsx
- **Header Improvements**: Navigation logic và settings link

### 📁 Files Modified:
- `backend/lib/routes/api/files/download/v1.0.js` - User authentication check
- `backend/lib/routes/api/auth/register/v1.0.js` - Password validation
- `frontend/src/App.tsx` - i18n import, settings route, LoginPageNew
- `frontend/src/components/Header.tsx` - Navigation logic, settings link
- `frontend/package.json` - i18n dependencies

### 📁 Files Added:
- `frontend/src/i18n/index.ts` - i18n configuration
- `frontend/src/i18n/locales/en.json` - English translations
- `frontend/src/i18n/locales/vi.json` - Vietnamese translations
- `frontend/src/i18n/locales/zh.json` - Chinese translations
- `frontend/src/components/SettingsPage.tsx` - Settings page component
- `frontend/src/components/LoginPageNew.tsx` - New login page with Material-UI
- `frontend/src/utils/passwordValidation.ts` - Password validation utility

---

## 🔄 Cập nhật triệt để (2025-01-18 - Lần 2)

### ✅ Các vấn đề đã được sửa triệt để:

1. **🔧 Sửa lỗi Download hoàn toàn**
   - Thay đổi từ streaming sang buffer download để tránh lỗi 2KB
   - Thêm authentication check nghiêm ngặt (chỉ user owner mới download được)
   - Thêm logging chi tiết để debug
   - Sử dụng `telegramService.downloadFile()` thay vì `getFileStream()`
   - Set proper headers và cache control

2. **🎯 Sửa Sidebar focus issue**
   - Thêm `isActive` function cho từng menu item
   - Chỉ highlight item thực sự active thay vì tất cả
   - Cập nhật logic để chỉ "Home" và "All files" active khi ở root folder
   - Fix deprecated `primaryTypographyProps` thành `slotProps`

3. **🌍 Hoàn thiện hệ thống i18n**
   - Thêm translations cho tất cả components: Sidebar, Breadcrumbs, ActionBar
   - Cập nhật 3 ngôn ngữ (EN, VI, ZH) với đầy đủ keys
   - Dynamic menu items và quick access items với translations
   - Settings page hiển thị đúng ngôn ngữ hiện tại (fallback 'en')

4. **🔐 Real-time password confirmation**
   - Thêm `passwordMatch` state để track password matching
   - Hiển thị error real-time khi passwords không khớp
   - Hiển thị success indicator khi passwords khớp
   - Clear validation khi user thay đổi input

### 🔧 Technical Improvements:

- **Download Security**: Chỉ cho phép user download file của chính họ
- **Buffer vs Stream**: Sử dụng buffer download thay vì streaming để tránh corruption
- **Component Translations**: Tất cả UI text đã được i18n
- **Sidebar Logic**: Cải thiện active state logic để tránh multiple highlights
- **Form Validation**: Real-time password confirmation với visual feedback
- **Material-UI Updates**: Fix deprecated props và sử dụng `slotProps`

### 📁 Files Modified (Lần 2):
- `backend/lib/routes/api/files/download/v1.0.js` - Complete download fix
- `frontend/src/components/Sidebar.tsx` - Fix focus issue, add i18n
- `frontend/src/components/Breadcrumbs.tsx` - Add i18n
- `frontend/src/components/ActionBar.tsx` - Add i18n
- `frontend/src/components/LoginPageNew.tsx` - Real-time password validation
- `frontend/src/components/SettingsPage.tsx` - Fix language display
- `frontend/src/i18n/locales/en.json` - Add sidebar, breadcrumbs, actionBar keys
- `frontend/src/i18n/locales/vi.json` - Add sidebar, breadcrumbs, actionBar keys
- `frontend/src/i18n/locales/zh.json` - Add sidebar, breadcrumbs, actionBar keys

### 🎯 Kết quả:
- ✅ Download files hoạt động bình thường, không còn lỗi 2KB
- ✅ Sidebar chỉ highlight đúng item được chọn
- ✅ Tất cả text trong UI đã được dịch đa ngôn ngữ
- ✅ Password confirmation hiển thị real-time feedback
- ✅ Settings page hiển thị đúng ngôn ngữ hiện tại

---

**Last Updated**: 2025-01-18
**Version**: 1.3.0
**Status**: In Development - Major Issues Fixed
