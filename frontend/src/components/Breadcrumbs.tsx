import React from 'react';
import {
  Breadcrumbs as MuiBreadcrumbs,
  Link,
  Typography,
  Box,
  useTheme,
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useApp } from '../contexts/AppContext';

interface BreadcrumbsProps {
  onNavigate: (folderId: string | null) => void;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ onNavigate }) => {
  const { state } = useApp();
  const theme = useTheme();
  const { t } = useTranslation();

  if (state.isSearching || state.searchQuery) {
    return (
      <Box sx={{ py: 1 }}>
        <Typography
          variant="h6"
          sx={{
            color: theme.palette.text.primary,
            fontSize: '1.25rem',
            fontWeight: 600,
          }}
        >
          {state.isSearching ? t('breadcrumbs.searching') : `${t('breadcrumbs.searchResults')} "${state.searchQuery}"`}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ py: 1 }}>
      <MuiBreadcrumbs
        separator={
          <NavigateNextIcon
            fontSize="small"
            sx={{ color: theme.palette.text.secondary, mx: 0.5 }}
          />
        }
        aria-label="breadcrumb"
      >
        {state.breadcrumbs.map((breadcrumb, index) => {
          const isLast = index === state.breadcrumbs.length - 1;
          const isHome = breadcrumb.id === null;

          if (isLast) {
            return (
              <Typography
                key={breadcrumb.id || 'home'}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: theme.palette.text.primary,
                  fontSize: '1.25rem',
                  fontWeight: 600,
                }}
              >
                {isHome && <HomeIcon sx={{ mr: 1, fontSize: '1.25rem' }} />}
                {breadcrumb.name}
              </Typography>
            );
          }

          return (
            <Link
              key={breadcrumb.id || 'home'}
              underline="hover"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                onNavigate(breadcrumb.id);
              }}
              sx={{
                display: 'flex',
                alignItems: 'center',
                color: theme.palette.text.secondary,
                fontSize: '1.25rem',
                fontWeight: 500,
                textDecoration: 'none',
                '&:hover': {
                  color: theme.palette.primary.main,
                  textDecoration: 'underline',
                },
              }}
            >
              {isHome && <HomeIcon sx={{ mr: 1, fontSize: '1.25rem' }} />}
              {breadcrumb.name}
            </Link>
          );
        })}
      </MuiBreadcrumbs>
    </Box>
  );
};

export default Breadcrumbs;
