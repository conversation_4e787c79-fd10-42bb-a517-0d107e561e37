import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  AlertT<PERSON>le,
  <PERSON>,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as <PERSON>rrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Download as DownloadIcon,
  OpenInNew as OpenIcon,
  Visibility as PreviewIcon,
} from '@mui/icons-material';

interface OfficePreviewHelperProps {
  fileInfo: any;
  onPreviewAttempt: (viewer: 'google' | 'microsoft') => void;
  onDownload: () => void;
  currentViewer?: 'google' | 'microsoft' | 'fallback';
  error?: string;
  loading?: boolean;
}

const OfficePreviewHelper: React.FC<OfficePreviewHelperProps> = ({
  fileInfo,
  onPreviewAttempt,
  onDownload,
  currentViewer = 'google',
  error,
  loading = false,
}) => {
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);

  const getViewerStatus = (viewer: 'google' | 'microsoft') => {
    if (currentViewer === viewer && error) {
      return { status: 'error', message: 'Failed to load' };
    } else if (currentViewer === viewer && loading) {
      return { status: 'loading', message: 'Loading...' };
    } else if (currentViewer === viewer && !error && !loading) {
      return { status: 'success', message: 'Active' };
    }
    return { status: 'available', message: 'Available' };
  };

  const googleStatus = getViewerStatus('google');
  const microsoftStatus = getViewerStatus('microsoft');

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'loading':
        return <InfoIcon color="info" />;
      default:
        return <InfoIcon color="action" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'loading':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
      {/* File Info */}
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Box sx={{ fontSize: 48, mb: 2, color: fileInfo?.color || '#2196F3' }}>
          {fileInfo?.icon || '📄'}
        </Box>
        <Typography variant="h6" gutterBottom>
          {fileInfo?.name}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {fileInfo?.displayType} • {fileInfo?.formattedSize}
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Preview Failed</AlertTitle>
          {error}
        </Alert>
      )}

      {/* Preview Options */}
      <Typography variant="h6" gutterBottom>
        Preview Options
      </Typography>
      
      <List sx={{ mb: 3 }}>
        {fileInfo?.previewOptions?.canPreviewWithGoogleDocs && (
          <ListItem>
            <ListItemIcon>
              {getStatusIcon(googleStatus.status)}
            </ListItemIcon>
            <ListItemText
              primary="Google Docs Viewer"
              secondary="Best compatibility for most Office documents"
            />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Chip
                label={googleStatus.message}
                color={getStatusColor(googleStatus.status) as any}
                size="small"
              />
              <Button
                size="small"
                variant="outlined"
                onClick={() => onPreviewAttempt('google')}
                disabled={loading && currentViewer === 'google'}
              >
                Try
              </Button>
            </Box>
          </ListItem>
        )}

        {fileInfo?.previewOptions?.canPreviewWithMicrosoftOffice && (
          <ListItem>
            <ListItemIcon>
              {getStatusIcon(microsoftStatus.status)}
            </ListItemIcon>
            <ListItemText
              primary="Microsoft Office Online"
              secondary="Alternative viewer for Office documents"
            />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Chip
                label={microsoftStatus.message}
                color={getStatusColor(microsoftStatus.status) as any}
                size="small"
              />
              <Button
                size="small"
                variant="outlined"
                onClick={() => onPreviewAttempt('microsoft')}
                disabled={loading && currentViewer === 'microsoft'}
              >
                Try
              </Button>
            </Box>
          </ListItem>
        )}

        <ListItem>
          <ListItemIcon>
            <DownloadIcon color="action" />
          </ListItemIcon>
          <ListItemText
            primary="Download & Open Locally"
            secondary="Download the file to view with your preferred application"
          />
          <Button
            size="small"
            variant="contained"
            onClick={onDownload}
            startIcon={<DownloadIcon />}
          >
            Download
          </Button>
        </ListItem>
      </List>

      {/* Troubleshooting */}
      <Divider sx={{ my: 2 }} />
      
      <Button
        variant="text"
        size="small"
        onClick={() => setShowTroubleshooting(!showTroubleshooting)}
        startIcon={<WarningIcon />}
      >
        {showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Tips
      </Button>

      {showTroubleshooting && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Common Issues & Solutions:
          </Typography>
          <Typography variant="body2" component="div">
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>If preview fails, try the alternative viewer</li>
              <li>Check your popup blocker settings for external preview</li>
              <li>Large files may take longer to load</li>
              <li>Some file formats may not be supported by online viewers</li>
              <li>Download the file if online preview doesn't work</li>
            </ul>
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default OfficePreviewHelper;
