import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Home as HomeIcon,
  Folder as FolderIcon,
  Image as ImageIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import StorageIndicator from './StorageIndicator';

interface SidebarProps {
  onNavigate: (folderId: string | null) => void;
  currentFolderId: string | null;
}

const menuItems = [
  {
    id: 'home',
    label: 'Home',
    icon: <HomeIcon />,
    folderId: null,
  },
  {
    id: 'files',
    label: 'All files',
    icon: <FolderIcon />,
    folderId: null,
  },
  {
    id: 'photos',
    label: 'Photos',
    icon: <ImageIcon />,
    folderId: null,
    disabled: true,
  },
  {
    id: 'shared',
    label: 'Shared',
    icon: <PeopleIcon />,
    folderId: null,
    disabled: true,
  },
  {
    id: 'requests',
    label: 'File requests',
    icon: <AssignmentIcon />,
    folderId: null,
    disabled: true,
  },
  {
    id: 'deleted',
    label: 'Deleted files',
    icon: <DeleteIcon />,
    folderId: null,
    disabled: true,
  },
];

const quickAccessItems = [
  {
    id: 'starred',
    label: 'Starred',
    icon: <StarIcon />,
    disabled: true,
  },
  {
    id: 'recent',
    label: 'Recent',
    icon: <ScheduleIcon />,
    disabled: true,
  },
];

const Sidebar: React.FC<SidebarProps> = ({ onNavigate, currentFolderId }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        width: 260,
        height: "calc(100vh - 64px)",
        backgroundColor: theme.palette.background.paper,
        borderRight: `1px solid ${theme.palette.divider}`,
        display: "flex",
        flexDirection: "column",
        position: "fixed",
        top: "64px",
        left: 0,
        zIndex: 1000,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      }}
    >
      <Box sx={{ p: 2 }}>
        <List sx={{ p: 1 }}>
          {menuItems.map((item) => (
            <ListItem key={item.id} disablePadding sx={{ mb: 1.5 }}>
              <ListItemButton
                onClick={() => !item.disabled && onNavigate(item.folderId)}
                disabled={item.disabled}
                sx={{
                  borderRadius: 1,
                  py: 1,
                  px: 1.5,
                  backgroundColor:
                    item.folderId === currentFolderId
                      ? theme.palette.primary.main + "20"
                      : "transparent",
                  "&:hover": {
                    backgroundColor:
                      item.folderId === currentFolderId
                        ? theme.palette.primary.main + "20"
                        : theme.palette.action.hover,
                  },
                  "&.Mui-disabled": {
                    opacity: 0.5,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 36,
                    color:
                      item.folderId === currentFolderId
                        ? theme.palette.primary.main
                        : theme.palette.text.secondary,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontSize: "0.875rem",
                    fontWeight: item.folderId === currentFolderId ? 600 : 400,
                    color:
                      item.folderId === currentFolderId
                        ? theme.palette.primary.main
                        : theme.palette.text.primary,
                  }}
                />
                {item.disabled && (
                  <Chip
                    label="Soon"
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: "0.6rem",
                      backgroundColor: theme.palette.action.hover,
                      color: theme.palette.text.secondary,
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2, borderColor: "#E5E7EB" }} />

        <Typography
          variant="body2"
          sx={{
            color: "#637381",
            fontWeight: 600,
            mb: 1.5,
            px: 1.5,
            fontSize: "0.75rem",
            textTransform: "uppercase",
            letterSpacing: "0.5px",
          }}
        >
          Quick access
        </Typography>

        <List sx={{ p: 1 }}>
          {quickAccessItems.map((item) => (
            <ListItem key={item.id} disablePadding sx={{ mb: 1 }}>
              <ListItemButton
                disabled={item.disabled}
                sx={{
                  borderRadius: 1,
                  py: 1,
                  px: 1.5,
                  "&:hover": {
                    backgroundColor: "#F3F4F6",
                  },
                  "&.Mui-disabled": {
                    opacity: 0.5,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 36,
                    color: "#637381",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontSize: "0.875rem",
                    color: "#637381",
                  }}
                />
                {item.disabled && (
                  <Chip
                    label="Soon"
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: "0.6rem",
                      backgroundColor: "#E5E7EB",
                      color: "#637381",
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Storage indicator */}
      <Box sx={{ mt: "auto", p: 2 }}>
        <StorageIndicator />
      </Box>
    </Box>
  );
};

export default Sidebar;
