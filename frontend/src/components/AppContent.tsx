import React, { useState, useEffect, useCallback } from 'react';
import { Box, useTheme, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, LinearProgress } from '@mui/material';

import { useApp } from '../contexts/AppContext';
import Sidebar from './Sidebar';
import ActionBar from './ActionBar';
import Breadcrumbs from './Breadcrumbs';
import FileGrid from './FileGrid';
import { Item, BreadcrumbItem } from '../types';
import { fileApi } from '../services/api';
import { validateFileName } from '../utils/helpers';
import { getErrorMessage } from '../utils/errorHandler';
import FilePreviewItem from './FilePreviewItem';

interface AppContentProps {
  uploadDialogOpen: boolean;
  setUploadDialogOpen: (open: boolean) => void;
}

const AppContent: React.FC<AppContentProps> = ({ uploadDialogOpen, setUploadDialogOpen }) => {
  const { state, dispatch } = useApp();
  const theme = useTheme();
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newItemName, setNewItemName] = useState('');
  const [itemToRename, setItemToRename] = useState<Item | null>(null);
  const [itemToDelete, setItemToDelete] = useState<Item | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({open: false, message: '', severity: 'success'});

  // Handle file selection
  const handleFileSelection = (fileList: FileList | null) => {
    if (fileList) {
      setSelectedFiles(Array.from(fileList));
    }
  };

  // Remove file from selection
  const removeFileFromSelection = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Load folder content
  const loadFolderContent = useCallback(async (folderId: string | null) => {
    console.log('🔄 Loading folder content for folderId:', folderId);
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      console.log('📡 Calling fileApi.browseFolder...');
      const response = await fileApi.browseFolder(folderId || undefined);
      console.log('✅ API Response:', response);

      if (response.code === 200 && response.data) {
        const allItems: Item[] = [...response.data.folders, ...response.data.files];
        console.log('📁 All items:', allItems);
        dispatch({ type: 'SET_ITEMS', payload: allItems });
        dispatch({ type: 'SET_CURRENT_FOLDER', payload: folderId });
      } else {
        console.log('❌ API response not successful:', response);
      }
    } catch (error: any) {
      console.error('💥 API Error:', error);
      dispatch({ type: 'SET_ERROR', payload: getErrorMessage(error, 'Failed to load folder content') });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []); // Remove dispatch dependency to prevent infinite loops

  // Build breadcrumbs
  const buildBreadcrumbs = (folderId: string | null, folderName: string | null): BreadcrumbItem[] => {
    if (!folderId) {
      return [{ id: null, name: 'Home' }];
    }
    return [
      { id: null, name: 'Home' },
      { id: folderId, name: folderName || 'Current Folder' },
    ];
  };

  // Navigate to folder
  const handleNavigate = (folderId: string | null, folderName?: string | null) => {
    const breadcrumbs = buildBreadcrumbs(folderId, folderName || null);
    dispatch({ type: 'SET_BREADCRUMBS', payload: breadcrumbs });
    dispatch({ type: 'CLEAR_SEARCH' });
    loadFolderContent(folderId);
  };

  // Handle item click
  const handleItemClick = (item: Item) => {
    if (item.type === 'folder') {
      handleNavigate(item.id, item.name);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);

    for (const file of selectedFiles) {
      dispatch({
        type: 'ADD_UPLOAD',
        payload: { fileName: file.name, progress: 0, status: 'uploading' },
      });

      try {
        await fileApi.uploadFile(
          file,
          state.currentFolderId || undefined,
          (progress) => {
            dispatch({
              type: 'UPDATE_UPLOAD',
              payload: { fileName: file.name, progress, status: 'uploading' },
            });
          }
        );

        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 100, status: 'completed' },
        });

        setTimeout(() => {
          dispatch({ type: 'REMOVE_UPLOAD', payload: file.name });
        }, 2000);

      } catch (error: any) {
        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 0, status: 'error' },
        });

        setSnackbar({
          open: true,
          message: `Failed to upload ${file.name}: ${getErrorMessage(error, 'Upload failed')}`,
          severity: 'error',
        });
      }
    }

    setIsUploading(false);
    setUploadDialogOpen(false);
    setSelectedFiles([]);
    loadFolderContent(state.currentFolderId);
  };

  // Handle create folder
  const handleCreateFolder = async () => {
    if (!validateFileName(newFolderName)) {
      setSnackbar({
        open: true,
        message: 'Invalid folder name',
        severity: 'error',
      });
      return;
    }

    try {
      await fileApi.createFolder(newFolderName, state.currentFolderId || undefined);
      setSnackbar({
        open: true,
        message: 'Folder created successfully',
        severity: 'success',
      });
      setCreateFolderDialogOpen(false);
      setNewFolderName('');
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: getErrorMessage(error, 'Failed to create folder'),
        severity: 'error',
      });
    }
  };

  // Handle rename item
  const handleRenameItem = async () => {
    if (!itemToRename || !validateFileName(newItemName)) {
      setSnackbar({
        open: true,
        message: 'Invalid name',
        severity: 'error',
      });
      return;
    }

    try {
      await fileApi.renameItem(itemToRename.id, newItemName);
      setSnackbar({
        open: true,
        message: 'Item renamed successfully',
        severity: 'success',
      });
      setRenameDialogOpen(false);
      setItemToRename(null);
      setNewItemName('');
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: getErrorMessage(error, 'Failed to rename item'),
        severity: 'error',
      });
    }
  };

  // Handle delete item
  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      await fileApi.deleteItem(itemToDelete.id);
      setSnackbar({
        open: true,
        message: 'Item deleted successfully',
        severity: 'success',
      });
      setDeleteDialogOpen(false);
      setItemToDelete(null);
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: getErrorMessage(error, 'Failed to delete item'),
        severity: 'error',
      });
    }
  };

  // Initialize app
  useEffect(() => {
    loadFolderContent(null);
  }, []); // Empty dependency array to run only once on mount

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: 'background.default',
      color: 'text.primary',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    }}>
      <Box sx={{
        mt: '64px', // Header height
        position: 'relative',
        minHeight: 'calc(100vh - 64px)',
        display: 'flex',
      }}>
        <Sidebar
          onNavigate={handleNavigate}
          currentFolderId={state.currentFolderId}
        />
        <Box sx={{
          ml: '260px', // Sidebar width
          flex: 1,
          p: 3,
          pt: 2,
          minHeight: 'calc(100vh - 64px)',
        }}>
            <Breadcrumbs onNavigate={handleNavigate} />
            <ActionBar
              onUploadClick={() => setUploadDialogOpen(true)}
              onCreateFolderClick={() => setCreateFolderDialogOpen(true)}
            />
            {state.loading && (
              <Box sx={{ mb: 3 }}>
                <LinearProgress
                  sx={{
                    borderRadius: 1,
                    height: 6,
                    backgroundColor: theme.palette.mode === 'dark'
                      ? 'rgba(96, 165, 250, 0.2)'
                      : 'rgba(37, 99, 235, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'primary.main',
                    },
                  }}
                />
              </Box>
            )}
            <FileGrid
              items={state.searchQuery ? state.searchResults : state.items}
              onItemClick={handleItemClick}
              onItemRename={(item) => {
                setItemToRename(item);
                setNewItemName(item.name);
                setRenameDialogOpen(true);
              }}
              onItemDelete={(item) => {
                setItemToDelete(item);
                setDeleteDialogOpen(true);
              }}
            />
        </Box>
      </Box>

        {/* Upload Progress */}
        {state.uploads.length > 0 && (
          <Box sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            width: 300,
            zIndex: 1300,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}>
            {state.uploads.map((upload) => (
              <Box
                key={upload.fileName}
                sx={{
                  p: 2,
                  backgroundColor: 'background.paper',
                  borderRadius: 2,
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 4px 20px rgba(0, 0, 0, 0.4)'
                    : '0 4px 20px rgba(148, 163, 184, 0.1)',
                }}
              >
                <Typography variant="body2" noWrap sx={{ mb: 1, fontWeight: 500 }}>
                  {upload.fileName}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={upload.progress}
                  color={upload.status === 'error' ? 'error' : 'primary'}
                  sx={{
                    borderRadius: 1,
                    height: 6,
                    backgroundColor: theme.palette.mode === 'dark'
                      ? 'rgba(96, 165, 250, 0.2)'
                      : 'rgba(37, 99, 235, 0.1)',
                  }}
                />
              </Box>
            ))}
          </Box>
        )}

        {/* Upload Dialog */}
        <Dialog
          open={uploadDialogOpen}
          onClose={() => {
            if (!isUploading) {
              setUploadDialogOpen(false);
              setSelectedFiles([]);
            }
          }}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              bgcolor: 'background.paper',
              boxShadow: theme.palette.mode === 'dark'
                ? '0 8px 32px rgba(0, 0, 0, 0.4)'
                : '0 8px 32px rgba(148, 163, 184, 0.2)',
            },
          }}
        >
          <DialogTitle sx={{ pb: 1, typography: 'h6', fontWeight: 600 }}>
            Upload Files {selectedFiles.length > 0 && `(${selectedFiles.length} selected)`}
          </DialogTitle>
          <DialogContent sx={{ pt: '16px !important', minHeight: '200px' }}>
            {selectedFiles.length === 0 ? (
              <input
                type="file"
                multiple
                onChange={(e) => handleFileSelection(e.target.files)}
                disabled={isUploading}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `2px dashed ${theme.palette.mode === 'dark' ? '#475569' : '#94a3b8'}`,
                  borderRadius: '8px',
                  backgroundColor: theme.palette.mode === 'dark' ? '#1e293b' : '#f8fafc',
                  cursor: isUploading ? 'not-allowed' : 'pointer',
                  textAlign: 'center',
                  color: theme.palette.mode === 'dark' ? '#cbd5e1' : '#64748b',
                }}
              />
            ) : (
              <Box>
                <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Selected files:
                  </Typography>
                  {!isUploading && (
                    <Button
                      size="small"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.multiple = true;
                        input.onchange = (e) => {
                          const target = e.target as HTMLInputElement;
                          if (target.files) {
                            setSelectedFiles(prev => [...prev, ...Array.from(target.files!)]);
                          }
                        };
                        input.click();
                      }}
                    >
                      Add More
                    </Button>
                  )}
                </Box>
                <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {selectedFiles.map((file, index) => (
                    <FilePreviewItem
                      key={`${file.name}-${index}`}
                      file={file}
                      onRemove={() => removeFileFromSelection(index)}
                      disabled={isUploading}
                    />
                  ))}
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedFiles([]);
              }}
              disabled={isUploading}
              sx={{
                color: 'text.secondary',
                '&:hover': { color: 'text.primary' },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleFileUpload}
              disabled={selectedFiles.length === 0 || isUploading}
              variant="contained"
              sx={{
                px: 3,
                bgcolor: 'primary.main',
                '&:hover': {
                  bgcolor: 'primary.dark',
                  transform: 'translateY(-1px)',
                },
                '&:disabled': {
                  bgcolor: 'grey.400',
                },
              }}
            >
              {isUploading ? 'Uploading...' : 'Upload'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create Folder Dialog */}
        <Dialog
          open={createFolderDialogOpen}
          onClose={() => setCreateFolderDialogOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              bgcolor: 'background.paper',
              boxShadow: theme.palette.mode === 'dark'
                ? '0 8px 32px rgba(0, 0, 0, 0.4)'
                : '0 8px 32px rgba(148, 163, 184, 0.2)',
            },
          }}
        >
          <DialogTitle sx={{ pb: 1, typography: 'h6', fontWeight: 600 }}>Create New Folder</DialogTitle>
          <DialogContent sx={{ pt: '16px !important' }}>
            <TextField
              autoFocus
              margin="dense"
              label="Folder Name"
              fullWidth
              variant="outlined"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: 2,
                  },
                },
              }}
            />
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => setCreateFolderDialogOpen(false)}
              sx={{
                color: 'text.secondary',
                '&:hover': { color: 'text.primary' },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateFolder}
              variant="contained"
              sx={{
                px: 3,
                bgcolor: 'primary.main',
                '&:hover': {
                  bgcolor: 'primary.dark',
                  transform: 'translateY(-1px)',
                },
              }}
            >
              Create
            </Button>
          </DialogActions>
        </Dialog>

        {/* Rename Dialog */}
        <Dialog
          open={renameDialogOpen}
          onClose={() => setRenameDialogOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              bgcolor: 'background.paper',
              boxShadow: theme.palette.mode === 'dark'
                ? '0 8px 32px rgba(0, 0, 0, 0.4)'
                : '0 8px 32px rgba(148, 163, 184, 0.2)',
            },
          }}
        >
          <DialogTitle sx={{ pb: 1, typography: 'h6', fontWeight: 600 }}>
            Rename {itemToRename?.type === 'folder' ? 'Folder' : 'File'}
          </DialogTitle>
          <DialogContent sx={{ pt: '16px !important' }}>
            <TextField
              autoFocus
              margin="dense"
              label="New Name"
              fullWidth
              variant="outlined"
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: 2,
                  },
                },
              }}
            />
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => setRenameDialogOpen(false)}
              sx={{
                color: 'text.secondary',
                '&:hover': { color: 'text.primary' },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRenameItem}
              variant="contained"
              sx={{
                px: 3,
                bgcolor: 'primary.main',
                '&:hover': {
                  bgcolor: 'primary.dark',
                  transform: 'translateY(-1px)',
                },
              }}
            >
              Rename
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              bgcolor: 'background.paper',
              boxShadow: theme.palette.mode === 'dark'
                ? '0 8px 32px rgba(0, 0, 0, 0.4)'
                : '0 8px 32px rgba(148, 163, 184, 0.2)',
            },
          }}
        >
          <DialogTitle sx={{ pb: 1, typography: 'h6', fontWeight: 600, color: 'error.main' }}>
            Delete {itemToDelete?.type === 'folder' ? 'Folder' : 'File'}
          </DialogTitle>
          <DialogContent sx={{ pt: '16px !important' }}>
            <Typography variant="body1" sx={{ color: 'text.primary' }}>
              Are you sure you want to delete "{itemToDelete?.name}"?
              {itemToDelete?.type === 'folder' && (
                <Typography component="span" sx={{ display: 'block', mt: 1, color: 'error.main' }}>
                  This will also delete all contents inside this folder.
                </Typography>
              )}
            </Typography>
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => setDeleteDialogOpen(false)}
              sx={{
                color: 'text.secondary',
                '&:hover': { color: 'text.primary' },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteItem}
              variant="contained"
              color="error"
              sx={{
                px: 3,
                '&:hover': {
                  bgcolor: 'error.dark',
                  transform: 'translateY(-1px)',
                },
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          sx={{ mb: 8 }} // Add margin bottom to avoid overlap with storage indicator
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            variant="filled"
            sx={{
              width: '100%',
              borderRadius: 2,
              boxShadow: theme.palette.mode === 'dark'
                ? '0 4px 20px rgba(0, 0, 0, 0.4)'
                : '0 4px 20px rgba(148, 163, 184, 0.1)',
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
    </Box>
  );
};

export default AppContent;
