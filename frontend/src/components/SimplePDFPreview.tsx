import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Download as DownloadIcon,
  Fullscreen as FullscreenIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
} from '@mui/icons-material';
import { Document, Page, pdfjs } from 'react-pdf';
import { fileApi } from '../services/api';
import { formatFileSize, downloadFile } from '../utils/helpers';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface SimplePDFPreviewProps {
  item: any;
  onLoadComplete: () => void;
}

const SimplePDFPreview: React.FC<SimplePDFPreviewProps> = ({ item, onLoadComplete }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState(1.0);
  const [viewMode, setViewMode] = useState<'pdf' | 'iframe' | 'info'>('pdf');
  const [retryCount, setRetryCount] = useState(0);

  const pdfUrl = fileApi.previewFile(item.id);

  useEffect(() => {
    setLoading(true);
    setError(null);
    setRetryCount(0);
  }, [item.id]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully:', numPages, 'pages');
    setNumPages(numPages);
    setLoading(false);
    setError(null);
    onLoadComplete();
  };

  const onDocumentLoadError = (err: any) => {
    console.error('PDF load error:', err);
    setLoading(false);
    
    if (retryCount < 2) {
      // Try iframe fallback first
      setViewMode('iframe');
      setRetryCount(prev => prev + 1);
    } else {
      setError('Failed to load PDF. Please try downloading the file.');
      onLoadComplete();
    }
  };

  const handleRetry = () => {
    if (viewMode === 'pdf') {
      setViewMode('iframe');
    } else if (viewMode === 'iframe') {
      setViewMode('info');
    } else {
      setViewMode('pdf');
      setRetryCount(0);
    }
    setLoading(true);
    setError(null);
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const handleFullscreen = () => {
    const downloadUrl = fileApi.downloadFile(item.id);
    window.open(downloadUrl, '_blank');
  };

  // PDF.js viewer
  if (viewMode === 'pdf') {
    return (
      <Box
        sx={{
          width: '100%',
          height: '70vh',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          overflow: 'hidden',
          position: 'relative',
          bgcolor: 'background.paper',
        }}
      >
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
              bgcolor: 'background.paper',
              p: 3,
              borderRadius: 2,
              boxShadow: 2,
            }}
          >
            <CircularProgress />
            <Typography variant="body2" color="text.secondary">
              Loading PDF with PDF.js...
            </Typography>
          </Box>
        )}

        {error && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              zIndex: 2,
              bgcolor: 'background.paper',
              p: 3,
              borderRadius: 2,
              boxShadow: 2,
              maxWidth: '80%',
            }}
          >
            <Typography variant="h6" color="error" gutterBottom>
              PDF Preview Failed
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {error}
            </Typography>
            
            <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={handleRetry}
              >
                Try Browser Viewer
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  const downloadUrl = fileApi.downloadFile(item.id);
                  downloadFile(downloadUrl, item.name);
                }}
              >
                Download PDF
              </Button>
            </Box>
          </Box>
        )}

        {!loading && !error && (
          <>
            {/* Toolbar */}
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                zIndex: 3,
                display: 'flex',
                gap: 1,
                bgcolor: 'background.paper',
                borderRadius: 1,
                boxShadow: 1,
                p: 0.5,
              }}
            >
              <Tooltip title="Zoom Out">
                <IconButton size="small" onClick={handleZoomOut} disabled={scale <= 0.5}>
                  <ZoomOutIcon />
                </IconButton>
              </Tooltip>
              <Typography variant="caption" sx={{ alignSelf: 'center', minWidth: 40, textAlign: 'center' }}>
                {Math.round(scale * 100)}%
              </Typography>
              <Tooltip title="Zoom In">
                <IconButton size="small" onClick={handleZoomIn} disabled={scale >= 3.0}>
                  <ZoomInIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Open in New Tab">
                <IconButton size="small" onClick={handleFullscreen}>
                  <FullscreenIcon />
                </IconButton>
              </Tooltip>
            </Box>

            {/* PDF Content */}
            <Box
              sx={{
                width: '100%',
                height: '100%',
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                py: 2,
              }}
            >
              <Document
                file={pdfUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={null}
                error={null}
                options={{
                  cMapUrl: 'cmaps/',
                  cMapPacked: true,
                  standardFontDataUrl: 'standard_fonts/',
                }}
              >
                <Page
                  pageNumber={pageNumber}
                  scale={scale}
                  width={Math.min(800, window.innerWidth * 0.8)}
                />
              </Document>

              {/* Navigation */}
              {numPages && numPages > 1 && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
                  <IconButton
                    onClick={() => setPageNumber(p => Math.max(1, p - 1))}
                    disabled={pageNumber <= 1}
                    size="small"
                  >
                    <PrevIcon />
                  </IconButton>
                  <Typography variant="body2" sx={{ minWidth: 100, textAlign: 'center' }}>
                    Page {pageNumber} of {numPages}
                  </Typography>
                  <IconButton
                    onClick={() => setPageNumber(p => Math.min(numPages, p + 1))}
                    disabled={pageNumber >= numPages}
                    size="small"
                  >
                    <NextIcon />
                  </IconButton>
                </Box>
              )}
            </Box>
          </>
        )}
      </Box>
    );
  }

  // Browser iframe fallback
  if (viewMode === 'iframe') {
    return (
      <Box
        sx={{
          width: '100%',
          height: '70vh',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <CircularProgress />
            <Typography variant="body2" color="text.secondary">
              Loading PDF with browser viewer...
            </Typography>
          </Box>
        )}

        <iframe
          src={pdfUrl}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            opacity: loading ? 0.3 : 1,
            transition: 'opacity 0.3s ease',
          }}
          title={item.name}
          onLoad={() => {
            console.log('PDF iframe loaded');
            setLoading(false);
            onLoadComplete();
          }}
          onError={() => {
            console.error('PDF iframe failed to load');
            setLoading(false);
            setError('Browser PDF viewer failed to load');
          }}
        />

        {error && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              zIndex: 2,
              bgcolor: 'background.paper',
              p: 3,
              borderRadius: 2,
              boxShadow: 2,
            }}
          >
            <Typography variant="h6" color="error" gutterBottom>
              Browser Viewer Failed
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {error}
            </Typography>
            
            <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={handleRetry}
              >
                Show File Info
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  const downloadUrl = fileApi.downloadFile(item.id);
                  downloadFile(downloadUrl, item.name);
                }}
              >
                Download PDF
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    );
  }

  // File info fallback
  return (
    <Box sx={{ textAlign: 'center', py: 4, width: '100%' }}>
      <Box sx={{ fontSize: 64, mb: 3, color: '#F44336' }}>
        📄
      </Box>
      <Typography variant="h6" gutterBottom>
        {item.name}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        PDF Document
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Size: {formatFileSize(item.size)}
      </Typography>

      <Alert severity="warning" sx={{ mt: 2, mb: 2, maxWidth: 400, mx: 'auto' }}>
        PDF preview is not available in your browser. Please download the file to view it.
      </Alert>

      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
        <Button
          variant="outlined"
          onClick={() => {
            setViewMode('pdf');
            setLoading(true);
            setError(null);
          }}
          sx={{ minWidth: 160 }}
        >
          Try PDF.js Viewer
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            setViewMode('iframe');
            setLoading(true);
            setError(null);
          }}
          sx={{ minWidth: 160 }}
        >
          Try Browser Viewer
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            const downloadUrl = fileApi.downloadFile(item.id);
            downloadFile(downloadUrl, item.name);
          }}
          sx={{ minWidth: 120 }}
          startIcon={<DownloadIcon />}
        >
          Download PDF
        </Button>
      </Box>

      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1, maxWidth: 500, mx: 'auto' }}>
        <Typography variant="caption" color="info.dark" sx={{ display: 'block', mb: 1 }}>
          <strong>PDF Preview Options:</strong>
        </Typography>
        <Typography variant="caption" color="info.dark" sx={{ display: 'block' }}>
          • PDF.js Viewer (recommended) • Browser Native Viewer • Download for local viewing
        </Typography>
      </Box>
    </Box>
  );
};

export default SimplePDFPreview;
