import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  useTheme,
  Paper,
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as PreviewIcon,
  Close as CloseIcon,
  Image as ImageIcon,
  Description as TextIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { Item, FileItem, FolderItem } from '../types';
import {
  formatFileSize,
  formatDate,
  isImageFile,
  getFileCategory,
  getFileIcon,
  downloadFile
} from '../utils/helpers';
import { fileApi } from '../services/api';

import SimpleOfficePreview from './SimpleOfficePreview';
import SimplePDFPreview from './SimplePDFPreview';

interface FileGridProps {
  items: Item[];
  onItemClick: (item: Item) => void;
  onItemRename: (item: Item) => void;
  onItemDelete: (item: Item) => void;
}

interface TextFilePreviewProps {
  item: FileItem;
  onLoadComplete: () => void;
}

const TextFilePreview: React.FC<TextFilePreviewProps> = ({ item, onLoadComplete }) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const theme = useTheme();

  useEffect(() => {
    const loadTextContent = async () => {
      try {
        setLoading(true);
        setError('');

        const textUrl = fileApi.previewFile(item.id);
        const response = await fetch(textUrl);

        if (!response.ok) {
          throw new Error(`Failed to load file: ${response.status}`);
        }

        const text = await response.text();
        setContent(text);
        onLoadComplete();
      } catch (err) {
        console.error('Error loading text file:', err);
        setError(err instanceof Error ? err.message : 'Failed to load file');
        onLoadComplete();
      } finally {
        setLoading(false);
      }
    };

    loadTextContent();
  }, [item.id, onLoadComplete]);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '60vh',
          gap: 2,
        }}
      >
        <CircularProgress />
        <Typography variant="body2" color="text.secondary">
          Loading {item.name}...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '60vh',
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Failed to load file
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {error}
        </Typography>
        <Button
          variant="outlined"
          onClick={() => {
            const downloadUrl = fileApi.downloadFile(item.id);
            downloadFile(downloadUrl, item.name);
          }}
        >
          Download Instead
        </Button>
      </Box>
    );
  }

  return (
    <Paper
      sx={{
        width: '100%',
        height: '60vh',
        overflow: 'auto',
        p: 2,
        backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e' : '#f8f9fa',
        border: '1px solid #e0e0e0',
        borderRadius: 2,
      }}
    >
      <Typography
        component="pre"
        sx={{
          fontFamily: 'Monaco, Consolas, "Courier New", monospace',
          fontSize: '0.875rem',
          lineHeight: 1.5,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          margin: 0,
          color: theme.palette.text.primary,
        }}
      >
        {content}
      </Typography>
    </Paper>
  );
};

const PDFFilePreview: React.FC<TextFilePreviewProps> = ({ item, onLoadComplete }) => {
  return <SimplePDFPreview item={item} onLoadComplete={onLoadComplete} />;
};

const OfficeFilePreview: React.FC<TextFilePreviewProps> = ({ item, onLoadComplete }) => {
  return <SimpleOfficePreview item={item} onLoadComplete={onLoadComplete} />;
};




const FileGrid: React.FC<FileGridProps> = ({
  items,
  onItemClick,
  onItemRename,
  onItemDelete,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewItem, setPreviewItem] = useState<FileItem | null>(null);
  const [previewLoading, setPreviewLoading] = useState(false);

  // Function to get preview URL with current token
  const getPreviewUrl = (fileId: string) => {
    const token = localStorage.getItem('token');
    return `/api/v1.0/files/preview/${fileId}${token ? `?token=${token}` : ''}`;
  };

  // Handle loading state and timeout
  useEffect(() => {
    if (previewItem) {
      setPreviewLoading(true);

      // Set timeout to stop loading after 5 seconds
      const timeout = setTimeout(() => {
        console.log('Preview timeout reached, stopping loading');
        setPreviewLoading(false);
      }, 10000);

      return () => clearTimeout(timeout);
    } else {
      setPreviewLoading(false);
    }
  }, [previewItem]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, item: Item) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const handleDownload = () => {
    if (selectedItem && selectedItem.type === 'file') {
      const fileItem = selectedItem as FileItem;
      const downloadUrl = fileApi.downloadFile(fileItem.id);
      downloadFile(downloadUrl, fileItem.name);
    }
    handleMenuClose();
  };

  const handleRename = () => {
    if (selectedItem) {
      onItemRename(selectedItem);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedItem) {
      onItemDelete(selectedItem);
    }
    handleMenuClose();
  };

  const handlePreview = () => {
    if (selectedItem && selectedItem.type === 'file') {
      setPreviewItem(selectedItem as FileItem);
      setPreviewLoading(true);
      setPreviewOpen(true);
    }
    handleMenuClose();
  };

  const handleItemClick = (item: Item) => {
    if (item.type === 'file') {
      setPreviewItem(item as FileItem);
      setPreviewLoading(true);
      setPreviewOpen(true);
    } else {
      onItemClick(item);
    }
  };

  const getFileTypeIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <ImageIcon />;
    if (mimeType.startsWith('text/') || mimeType.includes('json')) return <TextIcon />;
    if (mimeType.includes('javascript') || mimeType.includes('css')) return <CodeIcon />;
    return <FileIcon />;
  };

  const renderPreviewContent = (item: FileItem) => {
    const category = getFileCategory(item.mimeType);

    switch (category) {
      case 'image':
        return (
          <Box sx={{ position: 'relative', display: 'flex', justifyContent: 'center' }}>
            <img
              src={getPreviewUrl(item.id)}
              alt={item.name}
              style={{
                maxWidth: '100%',
                maxHeight: '60vh',
                objectFit: 'contain',
                borderRadius: 8,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                transition: 'opacity 0.3s ease',
              }}
              onLoad={() => {
                setPreviewLoading(false);
                console.log('Image loaded successfully:', item.name);
              }}
              onError={(e) => {
                setPreviewLoading(false);
                console.error('Failed to load image:', item.name);
                // Show error message or fallback
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
            {previewLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '200px',
                  minWidth: '200px',
                }}
              >
                <CircularProgress />
              </Box>
            )}
          </Box>
        );

      case 'video':
        return (
          <video
            src={getPreviewUrl(item.id)}
            controls
            style={{
              maxWidth: '100%',
              maxHeight: '60vh',
              borderRadius: 8,
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            }}
            onLoadedData={() => setPreviewLoading(false)}
            onError={() => setPreviewLoading(false)}
          >
            Your browser does not support the video tag.
          </video>
        );

      case 'audio':
        return (
          <Box sx={{ textAlign: 'center', py: 4, width: '100%', maxWidth: 500 }}>
            <Box sx={{ fontSize: 64, mb: 3, color: '#9C27B0' }}>
              🎵
            </Box>
            <Typography variant="h6" gutterBottom>
              {item.name}
            </Typography>
            <audio
              controls
              style={{ width: '100%', marginTop: 16 }}
              onLoadedData={() => setPreviewLoading(false)}
              onError={() => setPreviewLoading(false)}
            >
              <source src={getPreviewUrl(item.id)} type={item.mimeType} />
              Your browser does not support the audio tag.
            </audio>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Size: {formatFileSize(item.size)}
            </Typography>
          </Box>
        );

      case 'pdf':
        return <PDFFilePreview item={item} onLoadComplete={() => setPreviewLoading(false)} />;

      case 'text':
        return <TextFilePreview item={item} onLoadComplete={() => setPreviewLoading(false)} />;

      case 'office':
        return <OfficeFilePreview item={item} onLoadComplete={() => setPreviewLoading(false)} />;

      case 'archive':
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Box sx={{ fontSize: 64, mb: 3, color: '#FF9800' }}>
              📦
            </Box>
            <Typography variant="h6" gutterBottom>
              {item.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Size: {formatFileSize(item.size)}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Type: Archive File ({item.mimeType})
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
              Download to extract and view contents
            </Typography>
          </Box>
        );

      default:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Box sx={{ fontSize: 64, mb: 3, color: '#757575' }}>
              {getFileIcon(item.mimeType)}
            </Box>
            <Typography variant="h6" gutterBottom>
              {item.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Size: {formatFileSize(item.size)}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Type: {item.mimeType}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
              This file type cannot be previewed in browser
            </Typography>
            <Button
              variant="contained"
              onClick={() => {
                const downloadUrl = fileApi.downloadFile(item.id);
                downloadFile(downloadUrl, item.name);
              }}
              sx={{ mt: 2 }}
            >
              Download to View
            </Button>
          </Box>
        );
    }
  };

  const isPreviewable = () => {
    // Allow preview for all file types - we'll handle different types in the preview modal
    return true;
  };

  const renderItemIcon = (item: Item) => {
    if (item.type === 'folder') {
      return (
        <Box
          sx={{
            width: 48,
            height: 48,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#4285FF',
            borderRadius: 2,
            color: 'white',
            mb: 1,
          }}
        >
          <FolderIcon sx={{ fontSize: 24 }} />
        </Box>
      );
    }

    const fileItem = item as FileItem;

    // Show thumbnail for images
    if (isImageFile(fileItem.mimeType)) {
      return (
        <Box
          sx={{
            width: 48,
            height: 48,
            borderRadius: 1,
            overflow: 'hidden',
            mb: 1,
            position: 'relative',
            border: '1px solid #E5E7EB',
            backgroundColor: '#F3F4F6',
          }}
        >
          <img
            src={getPreviewUrl(fileItem.id)}
            alt={fileItem.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'opacity 0.2s ease',
            }}
            onLoad={(e) => {
              // Hide loading placeholder when image loads
              const target = e.target as HTMLImageElement;
              const placeholder = target.nextElementSibling as HTMLElement;
              if (placeholder) {
                placeholder.style.display = 'none';
              }
            }}
            onError={(e) => {
              // Show fallback icon if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const placeholder = target.nextElementSibling as HTMLElement;
              if (placeholder) {
                placeholder.style.display = 'flex';
              }
            }}
          />
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#F3F4F6',
              position: 'absolute',
              top: 0,
              left: 0,
              color: '#637381',
            }}
          >
            <ImageIcon sx={{ fontSize: 24 }} />
          </Box>
        </Box>
      );
    }

    // Show appropriate icon for other file types
    return (
      <Box
        sx={{
          width: 48,
          height: 48,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: fileItem.mimeType.startsWith('text/') || fileItem.mimeType.includes('json')
            ? '#10B981'
            : '#6B7280',
          borderRadius: 1,
          color: 'white',
          mb: 1,
        }}
      >
        {getFileTypeIcon(fileItem.mimeType)}
      </Box>
    );
  };



  if (items.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 200,
          color: 'text.secondary',
        }}
      >
        <Typography variant="h6">No files or folders found</Typography>
      </Box>
    );
  }

  return (
    <>
      {/* List View - Dropbox style */}
      <Box sx={{
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
        overflow: 'hidden',
        border: `1px solid ${theme.palette.divider}`,
        marginTop: '16px',
      }}>
        {/* Header */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 120px 120px 40px',
            gap: 2,
            p: 2,
            backgroundColor: theme.palette.action.hover,
            borderBottom: `1px solid ${theme.palette.divider}`,
            fontSize: '0.875rem',
            fontWeight: 600,
            color: theme.palette.text.primary,
          }}
        >
          <Typography variant="body2" sx={{ fontWeight: 600 }}>Name</Typography>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>Who can access</Typography>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>Modified</Typography>
          <Box />
        </Box>

        {/* Items */}
        {items.map((item) => (
          <Box
            key={item.id}
            sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 120px 120px 40px',
              gap: 2,
              p: 2,
              borderBottom: `1px solid ${theme.palette.divider}`,
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
              '&:last-child': {
                borderBottom: 'none',
              },
            }}
            onClick={() => handleItemClick(item)}
          >
            {/* Name column */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, minWidth: 0 }}>
              {renderItemIcon(item)}
              <Box sx={{ minWidth: 0, flex: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color: theme.palette.text.primary,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                  title={item.name}
                >
                  {item.name}
                </Typography>
                {item.type === 'file' && (
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.text.secondary,
                      fontSize: '0.75rem',
                    }}
                  >
                    {formatFileSize((item as FileItem).size)}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Who can access column */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ color: '#637381', fontSize: '0.875rem' }}>
                Only you
              </Typography>
            </Box>

            {/* Modified column */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ color: '#637381', fontSize: '0.875rem' }}>
                {item.type === 'folder'
                  ? formatDate((item as FolderItem).createdAt)
                  : formatDate((item as FileItem).uploadDate)
                }
              </Typography>
            </Box>

            {/* Actions column */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <IconButton
                size="small"
                onClick={(e) => handleMenuOpen(e, item)}
                sx={{
                  color: '#637381',
                  '&:hover': {
                    backgroundColor: '#F3F4F6',
                  },
                }}
              >
                <MoreVertIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
        ))}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedItem?.type === 'file' && isPreviewable() && (
          <MenuItem onClick={handlePreview}>
            <PreviewIcon sx={{ mr: 1 }} />
            Preview
          </MenuItem>
        )}
        {selectedItem?.type === 'file' && (
          <MenuItem onClick={handleDownload}>
            <DownloadIcon sx={{ mr: 1 }} />
            Download
          </MenuItem>
        )}
        <MenuItem onClick={handleRename}>
          <EditIcon sx={{ mr: 1 }} />
          Rename
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Preview Modal */}
      <Dialog
        open={previewOpen}
        onClose={() => {
          setPreviewOpen(false);
          setPreviewLoading(false);
        }}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 3,
            minHeight: '60vh',
          }
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: 2,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            {previewItem?.name}
          </Typography>
          <IconButton
            onClick={() => {
              setPreviewOpen(false);
              setPreviewLoading(false);
            }}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', minHeight: '50vh', position: 'relative' }}>
          {previewItem && (
            <>
              {/* Loading Overlay */}
              {previewLoading && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(5px)',
                    zIndex: 10,
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <CircularProgress size={60} thickness={4} />
                    <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
                      Loading preview...
                    </Typography>
                  </Box>
                </Box>
              )}

              <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 3 }}>
                {renderPreviewContent(previewItem)}
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, background: '#f8f9fa' }}>
          <Button
            onClick={() => {
              if (previewItem) {
                const downloadUrl = fileApi.downloadFile(previewItem.id);
                downloadFile(downloadUrl, previewItem.name);
              }
            }}
            variant="contained"
            startIcon={<DownloadIcon />}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Download
          </Button>
          <Button
            onClick={() => {
              setPreviewOpen(false);
              setPreviewLoading(false);
            }}
            variant="outlined"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FileGrid;
