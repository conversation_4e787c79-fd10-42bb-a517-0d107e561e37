import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Tab,
  Tabs,
  Alert,
  LinearProgress,
  FormHelperText,
  useTheme,
  ThemeProvider,
  createTheme,
  CssBaseline,
  useMediaQuery,
} from '@mui/material';
import { validatePassword, getPasswordStrengthText } from '../utils/passwordValidation';
import '../i18n';

interface LoginPageProps {
  onLogin: (token: string, user: any) => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loginMode, setLoginMode] = useState<'login' | 'register' | 'forgot-password' | 'reset-password'>('login');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Traditional login form state
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    email: '',
    name: '',
    phone: '',
    token: ''
  });

  // OAuth configuration (will be fetched from backend)
  const [oauthConfig, setOauthConfig] = useState({
    enabled: false,
    google: { enabled: false },
    telegram: { enabled: false }
  });

  useEffect(() => {
    // Check for OAuth errors in URL params
    const error = searchParams.get('error');
    const resetToken = searchParams.get('token');

    if (error) {
      switch (error) {
        case 'google_auth_failed':
          setError('Google authentication failed. Please try again.');
          break;
        case 'telegram_auth_failed':
          setError('Telegram authentication failed. Please try again.');
          break;
        case 'oauth_error':
          setError('Authentication error occurred. Please try again.');
          break;
        case 'token_error':
          setError('Token generation failed. Please try again.');
          break;
        default:
          setError('Authentication failed. Please try again.');
      }
    } else if (resetToken && window.location.pathname === '/reset-password') {
      setLoginMode('reset-password');
      setFormData(prev => ({ ...prev, token: resetToken }));
    }

    // Fetch OAuth configuration
    fetchOAuthConfig();
  }, [searchParams]);

  const fetchOAuthConfig = async () => {
    try {
      const response = await fetch('/api/v1.0/auth/oauth/config');
      if (response.ok) {
        const config = await response.json();
        if (config.code === 200) {
          setOauthConfig(config.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch OAuth config:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear errors when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleTraditionalLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const endpoint = loginMode === 'login' ? '/api/v1.0/auth/login' : '/api/v1.0/auth/register';
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.code === 200) {
        if (loginMode === 'login') {
          onLogin(data.data.token, data.data);
          navigate('/');
        } else {
          setSuccess(data.message || 'Registration successful! Please check your email to verify your account.');
          // Keep email for potential forgot password use
          const currentEmail = formData.email;
          setFormData({
            username: '',
            password: '',
            confirmPassword: '',
            email: currentEmail, // Keep email
            name: '',
            phone: '',
            token: ''
          });
          setLoginMode('login');
        }
      } else {
        setError(data.message?.body || data.message || 'Authentication failed');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/v1.0/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });

      const data = await response.json();

      if (data.code === 200) {
        setSuccess(data.message || 'If the email exists, a password reset link has been sent.');
      } else {
        setError(data.message || 'Failed to send reset email');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/v1.0/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: formData.token,
          password: formData.password
        }),
      });

      const data = await response.json();

      if (data.code === 200) {
        setSuccess(data.message || 'Password reset successfully. You can now login with your new password.');
        setLoginMode('login');
        setFormData({ username: '', password: '', confirmPassword: '', email: '', name: '', phone: '', token: '' });
      } else {
        setError(data.message || 'Failed to reset password');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    setIsLoading(true);
    window.location.href = '/auth/google';
  };

  // const handleTelegramLogin = () => {
  //   setIsLoading(true);
  //   window.location.href = '/auth/telegram';
  // };

  // Telegram Login Widget callback
  const onTelegramAuth = async (user: any) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/v1.0/auth/telegram/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(user),
      });

      const data = await response.json();

      if (data.code === 200) {
        onLogin(data.data.token, data.data.user);
        navigate('/');
      } else {
        setError(data.message || 'Telegram authentication failed');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Add Telegram Login Widget script
  useEffect(() => {
    if (oauthConfig.telegram.enabled) {
      const script = document.createElement('script');
      script.src = 'https://telegram.org/js/telegram-widget.js?22';
      script.setAttribute('data-telegram-login', 'your_bot_username'); // Will be replaced with actual bot username
      script.setAttribute('data-size', 'large');
      script.setAttribute('data-onauth', 'onTelegramAuth(user)');
      script.setAttribute('data-request-access', 'write');
      script.async = true;

      // Add callback to global scope
      (window as any).onTelegramAuth = onTelegramAuth;

      const telegramContainer = document.getElementById('telegram-login-container');
      if (telegramContainer) {
        telegramContainer.appendChild(script);
      }

      return () => {
        if (telegramContainer && script.parentNode) {
          telegramContainer.removeChild(script);
        }
        delete (window as any).onTelegramAuth;
      };
    }
  }, [oauthConfig.telegram.enabled, onTelegramAuth]);

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-header">
          <h1>TeleStore</h1>
          <p>Your secure file storage solution</p>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        {success && (
          <div className="success-message">
            {success}
          </div>
        )}

        {loginMode !== 'forgot-password' && loginMode !== 'reset-password' && (
          <div className="login-tabs">
            <button
              className={`tab-button ${loginMode === 'login' ? 'active' : ''}`}
              onClick={() => setLoginMode('login')}
            >
              Login
            </button>
            <button
              className={`tab-button ${loginMode === 'register' ? 'active' : ''}`}
              onClick={() => setLoginMode('register')}
            >
              Register
            </button>
          </div>
        )}

        {loginMode === 'forgot-password' && (
          <div className="login-header">
            <h2>Reset Password</h2>
            <p>
              {formData.email
                ? `We'll send a password reset link to ${formData.email}`
                : 'Enter your email to receive a password reset link'
              }
            </p>
          </div>
        )}

        {loginMode === 'reset-password' && (
          <div className="login-header">
            <h2>Set New Password</h2>
            <p>Enter your new password</p>
          </div>
        )}

        <form
          onSubmit={
            loginMode === 'forgot-password' ? handleForgotPassword :
            loginMode === 'reset-password' ? handleResetPassword :
            handleTraditionalLogin
          }
          className="login-form"
        >
          {(loginMode === 'login' || loginMode === 'register') && (
            <div className="form-group">
              <input
                type="text"
                name="username"
                placeholder="Username"
                value={formData.username}
                onChange={handleInputChange}
                required
              />
            </div>
          )}

          {(loginMode === 'forgot-password') && (
            <div className="form-group">
              <input
                type="email"
                name="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleInputChange}
                required
                style={{
                  backgroundColor: formData.email ? '#f0f9ff' : 'white',
                  borderColor: formData.email ? '#0061FF' : '#e1e5e9'
                }}
              />
              {formData.email && (
                <small style={{
                  color: '#0061FF',
                  fontSize: '0.75rem',
                  marginTop: '4px',
                  display: 'block'
                }}>
                  ✓ Email pre-filled from your previous entry
                </small>
              )}
            </div>
          )}

          {loginMode === 'register' && (
            <>
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  placeholder="Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <input
                  type="text"
                  name="name"
                  placeholder="Full Name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <input
                  type="tel"
                  name="phone"
                  placeholder="Phone (optional)"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>
            </>
          )}

          {(loginMode === 'login' || loginMode === 'register' || loginMode === 'reset-password') && (
            <div className="form-group password-group">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder={loginMode === 'reset-password' ? 'New Password' : 'Password'}
                value={formData.password}
                onChange={handleInputChange}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          )}

          {(loginMode === 'register' || loginMode === 'reset-password') && (
            <div className="form-group password-group">
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmPassword"
                placeholder="Confirm Password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          )}

          <button
            type="submit"
            className="login-button"
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' :
              loginMode === 'login' ? 'Login' :
              loginMode === 'register' ? 'Register' :
              loginMode === 'forgot-password' ? 'Send Reset Link' :
              loginMode === 'reset-password' ? 'Reset Password' : 'Submit'
            }
          </button>

          {loginMode === 'login' && (
            <div className="form-links">
              <button
                type="button"
                className="link-button"
                onClick={() => {
                  setLoginMode('forgot-password');
                  // Pre-fill email if available from previous form data
                  // The email field will remain filled if user had entered it
                }}
              >
                Forgot Password?
              </button>
            </div>
          )}

          {(loginMode === 'forgot-password' || loginMode === 'reset-password') && (
            <div className="form-links">
              <button
                type="button"
                className="link-button"
                onClick={() => setLoginMode('login')}
              >
                Back to Login
              </button>
            </div>
          )}
        </form>

        {oauthConfig.enabled && (loginMode === 'login' || loginMode === 'register') && (
          <div className="oauth-section">
            <div className="divider">
              <span>or continue with</span>
            </div>

            <div className="oauth-buttons">
              {oauthConfig.google.enabled && (
                <button
                  onClick={handleGoogleLogin}
                  className="oauth-button google-button"
                  disabled={isLoading}
                >
                  <svg className="oauth-icon" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                </button>
              )}

              {oauthConfig.telegram.enabled && (
                <div className="telegram-login-wrapper">
                  <div id="telegram-login-container"></div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginPage;
