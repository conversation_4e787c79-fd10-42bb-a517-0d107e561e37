import React, { useState, useMemo, useEffect } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  useMediaQuery,
} from '@mui/material';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AppProvider } from './contexts/AppContext';
import AppContent from './components/AppContent';
import Header from './components/Header';
import LoginPage from './components/LoginPage';
import LoginSuccess from './components/LoginSuccess';
import EmailVerificationBanner from './components/EmailVerificationBanner';
import EmailVerificationPage from './components/EmailVerificationPage';
import SettingsPage from './components/SettingsPage';
import './i18n';

const App: React.FC = () => {
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');

  // Initialize theme from localStorage first, then fallback to system preference
  const getInitialTheme = (): 'light' | 'dark' => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'light' || savedTheme === 'dark') {
      return savedTheme;
    }
    return prefersDarkMode ? 'dark' : 'light';
  };

  const [mode, setMode] = useState<'light' | 'dark'>(getInitialTheme());
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing authentication on app load
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Failed to parse user data:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }

    setIsLoading(false);
  }, []);

  const handleLogin = (token: string, userData: any) => {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);
    setIsAuthenticated(true);
  };

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        // Call logout API to invalidate token on server
        await fetch('/api/v1.0/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'token': token
          }
        });
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with logout even if API call fails
    } finally {
      // Clear local storage and state
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode,
          primary: {
            main: mode === 'dark' ? '#60a5fa' : '#2563eb',
            light: mode === 'dark' ? '#93c5fd' : '#3b82f6',
            dark: mode === 'dark' ? '#2563eb' : '#1d4ed8',
          },
          secondary: {
            main: mode === 'dark' ? '#a78bfa' : '#7c3aed',
            light: mode === 'dark' ? '#c4b5fd' : '#8b5cf6',
            dark: mode === 'dark' ? '#7c3aed' : '#6d28d9',
          },
          background: {
            default: mode === 'dark' ? '#0f172a' : '#f8fafc',
            paper: mode === 'dark' ? '#1e293b' : '#ffffff',
          },
          text: {
            primary: mode === 'dark' ? '#f1f5f9' : '#1e293b',
            secondary: mode === 'dark' ? '#cbd5e1' : '#64748b',
          },
          divider: mode === 'dark' ? '#334155' : '#e2e8f0',
          action: {
            hover: mode === 'dark' ? '#334155' : '#f1f5f9',
          },
        },
        typography: {
          fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          h1: { fontWeight: 600 },
          h2: { fontWeight: 600 },
          h3: { fontWeight: 600 },
          h4: { fontWeight: 600 },
          h5: { fontWeight: 600 },
          h6: { fontWeight: 600 },
        },
        shape: {
          borderRadius: 12,
        },
        components: {
          MuiCssBaseline: {
            styleOverrides: {
              body: {
                scrollbarColor: mode === 'dark' ? '#475569 #1e293b' : '#94a3b8 #f1f5f9',
                '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
                  width: '8px',
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
                  borderRadius: 8,
                  backgroundColor: mode === 'dark' ? '#475569' : '#94a3b8',
                  minHeight: 24,
                  '&:hover': {
                    backgroundColor: mode === 'dark' ? '#64748b' : '#64748b',
                  },
                },
                '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
                  borderRadius: 8,
                  backgroundColor: mode === 'dark' ? '#1e293b' : '#f1f5f9',
                },
              },
            },
          },
          MuiContainer: {
            styleOverrides: {
              root: {
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              },
            },
          },
          MuiButton: {
            styleOverrides: {
              root: {
                textTransform: 'none',
                borderRadius: '10px',
                padding: '10px 20px',
                fontWeight: 500,
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: mode === 'dark'
                    ? '0 4px 12px rgba(148, 163, 184, 0.1)'
                    : '0 4px 12px rgba(148, 163, 184, 0.2)',
                },
              },
            },
          },
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: 16,
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: mode === 'dark'
                    ? '0 8px 24px rgba(148, 163, 184, 0.1)'
                    : '0 8px 24px rgba(148, 163, 184, 0.2)',
                },
              },
            },
          },
        },
      }),
    [mode]
  );

  const toggleColorMode = () => {
    setMode((prevMode) => {
      const newMode = prevMode === 'light' ? 'dark' : 'light';
      // Save theme preference to localStorage
      localStorage.setItem('theme', newMode);
      return newMode;
    });
  };

  if (isLoading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          background: mode === 'dark' ? '#0f172a' : '#f8fafc'
        }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid rgba(37, 99, 235, 0.3)',
            borderTop: '3px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          <Route
            path="/login"
            element={
              isAuthenticated ?
                <Navigate to="/" replace /> :
                <LoginPage onLogin={handleLogin} />
            }
          />
          <Route
            path="/reset-password"
            element={
              isAuthenticated ?
                <Navigate to="/" replace /> :
                <LoginPage onLogin={handleLogin} />
            }
          />
          <Route
            path="/verify-email"
            element={<EmailVerificationPage />}
          />
          <Route
            path="/login-success"
            element={<LoginSuccess onLogin={handleLogin} />}
          />
          <Route
            path="/settings"
            element={
              isAuthenticated ? (
                <AppProvider>
                  <Header
                    onToggleTheme={toggleColorMode}
                    onUploadClick={() => setUploadDialogOpen(true)}
                    user={user}
                    onLogout={handleLogout}
                  />
                  <SettingsPage
                    onThemeChange={toggleColorMode}
                    currentTheme={mode}
                  />
                </AppProvider>
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
          <Route
            path="/*"
            element={
              isAuthenticated ? (
                <AppProvider>
                  <Header
                    onToggleTheme={toggleColorMode}
                    onUploadClick={() => setUploadDialogOpen(true)}
                    user={user}
                    onLogout={handleLogout}
                  />
                  <EmailVerificationBanner
                    user={user}
                    onResendVerification={() => {}}
                  />
                  <AppContent
                    uploadDialogOpen={uploadDialogOpen}
                    setUploadDialogOpen={setUploadDialogOpen}
                  />
                </AppProvider>
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
        </Routes>
      </Router>
    </ThemeProvider>
  );
};

export default App;
