const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');
const storageService = require('../../../../services/storageService');
const fs = require('fs');
const path = require('path');

module.exports = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'No file uploaded'
      });
    }

      const { originalname, filename, path: filePath, size, mimetype } = req.file;
      const parentId = req.body.parentId || null;
      const userId = req.user ? req.user.id : null;

      // Check storage quota before upload
      const quotaCheck = await storageService.checkStorageQuota(userId, size);
      if (!quotaCheck.hasEnoughSpace) {
        // Clean up uploaded file
        fs.unlinkSync(filePath);
        return res.status(413).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: `Storage quota exceeded. You need ${storageService.formatBytes(quotaCheck.wouldExceedBy)} more space. Available: ${storageService.formatBytes(quotaCheck.availableSpace)}`,
          data: {
            storageUsed: quotaCheck.storageUsed,
            storageQuota: quotaCheck.storageQuota,
            availableSpace: quotaCheck.availableSpace,
            fileSize: quotaCheck.fileSize,
            wouldExceedBy: quotaCheck.wouldExceedBy
          }
        });
      }

      // Validate parent folder if provided
      if (parentId) {
        const FolderModel = require('../../../../models/folder');
        const parentFolder = await FolderModel.findOne({
          _id: parentId,
          isDeleted: false,
          ownerId: userId // Ensure user can only upload to their own folders
        });

        if (!parentFolder) {
          // Clean up uploaded file
          fs.unlinkSync(filePath);
          return res.status(400).json({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'Parent folder not found or access denied'
          });
        }
      }

      // Upload to Telegram and get full response
      const telegramResponse = await telegramService.uploadFileWithMetadata(filePath, originalname);

      // Extract file metadata from Telegram response
      const fileMetadata = {};
      if (telegramResponse.document) {
        fileMetadata.fileUniqueId = telegramResponse.document.file_unique_id;
        fileMetadata.fileName = telegramResponse.document.file_name;
        if (telegramResponse.document.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.document.thumbnail.file_id;
          fileMetadata.width = telegramResponse.document.thumbnail.width;
          fileMetadata.height = telegramResponse.document.thumbnail.height;
        }
      } else if (telegramResponse.video) {
        fileMetadata.fileUniqueId = telegramResponse.video.file_unique_id;
        fileMetadata.fileName = telegramResponse.video.file_name;
        fileMetadata.width = telegramResponse.video.width;
        fileMetadata.height = telegramResponse.video.height;
        fileMetadata.duration = telegramResponse.video.duration;
        if (telegramResponse.video.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.video.thumbnail.file_id;
        }
      } else if (telegramResponse.photo) {
        const largestPhoto = telegramResponse.photo[telegramResponse.photo.length - 1];
        fileMetadata.fileUniqueId = largestPhoto.file_unique_id;
        fileMetadata.width = largestPhoto.width;
        fileMetadata.height = largestPhoto.height;
      } else if (telegramResponse.audio) {
        fileMetadata.fileUniqueId = telegramResponse.audio.file_unique_id;
        fileMetadata.fileName = telegramResponse.audio.file_name;
        fileMetadata.duration = telegramResponse.audio.duration;
        fileMetadata.performer = telegramResponse.audio.performer;
        fileMetadata.title = telegramResponse.audio.title;
        if (telegramResponse.audio.thumbnail) {
          fileMetadata.thumbnail = telegramResponse.audio.thumbnail.file_id;
        }
      }

      // Create file record in database
      const FileModel = require('../../../../models/file');
      const fileRecord = new FileModel({
        telegramFileId: telegramResponse.fileId,
        originalFileName: originalname,
        fileSize: size,
        mimeType: mimetype,
        parentId: parentId || null,
        ownerId: userId,
        uploadDate: new Date(),
        telegramMetadata: telegramResponse.fullResponse, // Store complete Telegram response
        fileMetadata: fileMetadata
      });

      await fileRecord.save();

      // Update user storage usage
      await storageService.addStorageUsage(userId, size);

      // Clean up local file
      fs.unlinkSync(filePath);

      // Clear cache for parent folder
      await cacheService.clearFolderCache(parentId);
      await cacheService.clearSearchCache();

      global.logger.logInfo(`File uploaded successfully: ${originalname}, ID: ${fileRecord._id}, Size: ${size} bytes`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: fileRecord._id,
        telegramFileId: fileRecord.telegramFileId,
        originalFileName: fileRecord.originalFileName,
        fileSize: fileRecord.fileSize,
        mimeType: fileRecord.mimeType,
        uploadDate: fileRecord.uploadDate,
        parentId: fileRecord.parentId
      },
      message: 'File uploaded successfully'
    });

  } catch (error) {
    logger.logInfo(['files/upload error', error.message], __dirname);
    console.error('Upload error:', error);

    // Clean up local file if exists
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        logger.logInfo(['files/upload cleanup error', cleanupError.message], __dirname);
        console.error('Cleanup error:', cleanupError);
      }
    }

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
