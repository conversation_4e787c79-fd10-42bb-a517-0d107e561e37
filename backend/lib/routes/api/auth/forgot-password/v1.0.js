const _ = require('lodash');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');

module.exports = async (req, res) => {
  try {
    const { email } = req.body;

    // Check params
    if (!email) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Email is required'
      });
    }

    // Find user by email
    const user = await UserModel.findOne({
      email,
      status: 1
    });

    if (!user) {
      // Don't reveal if email exists or not for security
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        message: 'If the email exists, a password reset link has been sent.'
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour

    // Save reset token to user
    await UserModel.updateOne(
      { _id: user._id },
      {
        passwordResetToken: resetToken,
        passwordResetExpires: resetTokenExpiry,
        updatedAt: Date.now()
      }
    );

    // Send email if email service is configured
    try {
      const emailConfig = config.get('emailInfos')[0];
      if (emailConfig && emailConfig.auth && emailConfig.auth.user) {
        const transporter = nodemailer.createTransport({
          service: emailConfig.service,
          auth: emailConfig.auth
        });

        const resetUrl = `http://localhost:3001/reset-password?token=${resetToken}`;

        const mailOptions = {
          from: emailConfig.auth.user,
          to: email,
          subject: 'Password Reset - TeleStore',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2563eb;">Password Reset Request</h2>
              <p>You requested a password reset for your TeleStore account.</p>
              <p>Click the link below to reset your password:</p>
              <a href="${resetUrl}" style="display: inline-block; padding: 12px 24px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 16px 0;">Reset Password</a>
              <p>This link will expire in 1 hour.</p>
              <p>If you didn't request this, please ignore this email.</p>
              <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">TeleStore - Your secure file storage solution</p>
            </div>
          `
        };

        await transporter.sendMail(mailOptions);
        console.log('Password reset email sent to:', email);
      } else {
        console.log('Email service not configured, reset token generated but not sent');
      }
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the request if email fails
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'If the email exists, a password reset link has been sent.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
